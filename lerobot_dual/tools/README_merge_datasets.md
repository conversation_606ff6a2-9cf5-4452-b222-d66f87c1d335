# LeRobot数据集合并工具

这个工具允许您将多个LeRobot数据集合并成一个统一的数据集。它基于现有的`remove_episodes.py`工具开发，提供了完整的数据集合并功能。

## 功能特性

- ✅ 支持合并多个LeRobot数据集
- ✅ 自动重新编号episodes和frames索引
- ✅ 保持数据完整性和一致性
- ✅ 支持视频文件和parquet数据文件
- ✅ 数据集兼容性验证
- ✅ 备份功能保护现有数据
- ✅ 干运行模式用于预先验证
- ✅ 详细的日志记录和错误处理
- ✅ 支持本地数据集（无需HuggingFace Hub）

## 安装要求

确保您已经安装了LeRobot及其依赖项：

```bash
# 基本依赖
pip install pandas
pip install datasets
pip install jsonlines

# LeRobot相关依赖
# (假设您已经有LeRobot环境)
```

## 使用方法

### 1. 命令行使用

#### 基本用法
```bash
python merge_datasets.py \
  --datasets /path/to/dataset1 /path/to/dataset2 \
  --output /path/to/merged_dataset
```

#### 高级用法
```bash
python merge_datasets.py \
  --datasets /path/to/dataset1 /path/to/dataset2 /path/to/dataset3 \
  --output /path/to/merged_dataset \
  --repo-id my_merged_dataset \
  --backup
```

#### 干运行模式（仅验证兼容性）
```bash
python merge_datasets.py \
  --datasets /path/to/dataset1 /path/to/dataset2 \
  --output /path/to/merged_dataset \
  --dry-run
```

#### 使用示例数据集
```bash
# 首先复制示例数据集创建第二个数据集
cp -r lerobot_dual/tools/so101_dual_55 lerobot_dual/tools/so101_dual_55_copy

# 然后执行合并
python lerobot_dual/tools/merge_datasets.py \
  --datasets lerobot_dual/tools/so101_dual_55 lerobot_dual/tools/so101_dual_55_copy \
  --output lerobot_dual/tools/merged_so101_dual \
  --repo-id merged_so101_dual \
  --backup
```

### 2. Python API使用

```python
from pathlib import Path
from merge_datasets import merge_datasets

# 定义数据集路径
dataset_paths = [
    Path("path/to/dataset1"),
    Path("path/to/dataset2"),
    Path("path/to/dataset3")
]

# 执行合并
merged_dataset = merge_datasets(
    dataset_paths=dataset_paths,
    output_path=Path("path/to/merged_dataset"),
    output_repo_id="my_merged_dataset",
    backup=True
)

print(f"合并完成！总episodes: {merged_dataset.meta.total_episodes}")
```

## 参数说明

### 命令行参数

- `--datasets`: 要合并的数据集路径列表（必需，至少2个）
- `--output`: 输出数据集的路径（必需）
- `--repo-id`: 输出数据集的repo_id（可选，默认: "merged_dataset"）
- `--backup`: 如果输出路径已存在，是否创建备份（可选）
- `--dry-run`: 仅验证数据集兼容性，不执行实际合并（可选）

### Python API参数

- `dataset_paths`: 要合并的数据集路径列表
- `output_path`: 输出数据集的路径
- `output_repo_id`: 输出数据集的repo_id
- `backup`: 是否创建备份

## 数据集兼容性要求

为了成功合并数据集，所有输入数据集必须满足以下条件：

1. **特征结构一致**: 所有数据集必须具有相同的特征（features）结构
2. **数据类型匹配**: 对应特征的数据类型必须相同
3. **文件格式统一**: 所有数据集必须使用相同的文件格式（parquet + 可选视频）

工具会自动验证这些条件，如果发现不兼容会报错并停止合并。

## 合并过程说明

1. **验证输入**: 检查所有数据集路径是否存在且有效
2. **兼容性检查**: 验证数据集特征结构是否兼容
3. **创建输出目录**: 建立合并后数据集的目录结构
4. **重新编号**: 为所有episodes分配新的连续索引
5. **复制数据**: 复制parquet文件和视频文件到新位置
6. **更新元数据**: 生成合并后的元数据文件
7. **验证结果**: 确保合并后的数据集完整有效

## 测试

运行测试脚本验证功能：

```bash
# 运行基本测试
python test_merge_datasets.py

# 查看使用示例
python example_merge_usage.py
```

## 注意事项

1. **备份重要**: 合并操作会修改数据，建议始终使用`--backup`选项
2. **磁盘空间**: 确保有足够的磁盘空间存储合并后的数据集
3. **内存使用**: 大型数据集合并可能需要较多内存
4. **索引重建**: 合并后的数据集会有新的episode和frame索引
5. **元数据更新**: 所有统计信息和元数据都会重新计算

## 故障排除

### 常见错误

1. **"至少需要两个数据集进行合并"**
   - 确保提供了至少两个不同的数据集路径

2. **"数据集特征结构不兼容"**
   - 检查所有数据集是否具有相同的特征定义
   - 使用`--dry-run`模式预先验证

3. **"数据集路径不存在"**
   - 验证所有提供的路径是否正确
   - 确保数据集目录包含必要的元数据文件

4. **"磁盘空间不足"**
   - 清理磁盘空间或选择其他输出位置

### 调试技巧

- 使用`--dry-run`模式预先检查兼容性
- 检查日志输出获取详细错误信息
- 先用小数据集测试合并流程
- 确保所有依赖项正确安装

## 文件结构

```
lerobot_dual/tools/
├── merge_datasets.py          # 主合并工具
├── test_merge_datasets.py     # 测试脚本
├── example_merge_usage.py     # 使用示例
├── README_merge_datasets.md   # 本文档
└── so101_dual_55/            # 示例数据集
    ├── meta/
    │   ├── info.json
    │   ├── episodes.jsonl
    │   ├── tasks.jsonl
    │   └── episodes_stats.jsonl
    ├── data/
    └── videos/
```

## 贡献

如果您发现bug或有改进建议，请：

1. 检查现有的issues
2. 创建详细的bug报告或功能请求
3. 提供复现步骤和示例数据
4. 考虑提交pull request

## 许可证

本工具遵循与LeRobot相同的Apache 2.0许可证。
