#!/usr/bin/env python

"""
测试数据集合并功能的脚本
"""

import logging
import tempfile
from pathlib import Path
import shutil

from merge_datasets import merge_datasets, _load_local_dataset_safely
from lerobot.common.utils.utils import init_logging


def test_merge_with_sample_dataset():
    """使用提供的示例数据集测试合并功能"""
    
    # 示例数据集路径
    sample_dataset_path = Path("so101_dual_55")
    
    if not sample_dataset_path.exists():
        logging.error(f"示例数据集不存在: {sample_dataset_path}")
        return False
    
    logging.info("开始测试数据集合并功能...")
    
    # 创建临时目录用于测试
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建两个测试数据集（复制示例数据集）
        test_dataset1_path = temp_path / "test_dataset1"
        test_dataset2_path = temp_path / "test_dataset2"
        merged_dataset_path = temp_path / "merged_dataset"
        
        # 复制示例数据集作为测试数据
        logging.info("准备测试数据...")
        shutil.copytree(sample_dataset_path, test_dataset1_path)
        shutil.copytree(sample_dataset_path, test_dataset2_path)
        
        # 加载原始数据集信息
        original_dataset = _load_local_dataset_safely("test_dataset", sample_dataset_path)
        logging.info(f"原始数据集信息:")
        logging.info(f"  Episodes: {original_dataset.meta.total_episodes}")
        logging.info(f"  Frames: {original_dataset.meta.total_frames}")
        logging.info(f"  Tasks: {original_dataset.meta.total_tasks}")
        
        try:
            # 执行合并
            logging.info("执行数据集合并...")
            merged_dataset = merge_datasets(
                dataset_paths=[test_dataset1_path, test_dataset2_path],
                output_path=merged_dataset_path,
                output_repo_id="test_merged_dataset",
                backup=False
            )
            
            # 验证合并结果
            logging.info("验证合并结果...")
            expected_episodes = original_dataset.meta.total_episodes * 2
            expected_frames = original_dataset.meta.total_frames * 2
            
            if merged_dataset.meta.total_episodes == expected_episodes:
                logging.info(f"✓ Episodes数量正确: {merged_dataset.meta.total_episodes}")
            else:
                logging.error(f"✗ Episodes数量错误: 期望 {expected_episodes}, 实际 {merged_dataset.meta.total_episodes}")
                return False
            
            if merged_dataset.meta.total_frames == expected_frames:
                logging.info(f"✓ Frames数量正确: {merged_dataset.meta.total_frames}")
            else:
                logging.error(f"✗ Frames数量错误: 期望 {expected_frames}, 实际 {merged_dataset.meta.total_frames}")
                return False
            
            # 检查文件是否存在
            meta_files = ["info.json", "episodes.jsonl", "tasks.jsonl"]
            for file_name in meta_files:
                file_path = merged_dataset_path / "meta" / file_name
                if file_path.exists():
                    logging.info(f"✓ 元数据文件存在: {file_name}")
                else:
                    logging.error(f"✗ 元数据文件缺失: {file_name}")
                    return False
            
            # 检查数据文件
            data_dir = merged_dataset_path / "data" / "chunk-000"
            if data_dir.exists():
                parquet_files = list(data_dir.glob("*.parquet"))
                if len(parquet_files) == expected_episodes:
                    logging.info(f"✓ 数据文件数量正确: {len(parquet_files)}")
                else:
                    logging.error(f"✗ 数据文件数量错误: 期望 {expected_episodes}, 实际 {len(parquet_files)}")
                    return False
            else:
                logging.error("✗ 数据目录不存在")
                return False
            
            logging.info("✓ 数据集合并测试通过！")
            return True
            
        except Exception as e:
            logging.error(f"✗ 数据集合并测试失败: {e}")
            return False


def test_dry_run():
    """测试干运行模式"""
    sample_dataset_path = Path("so101_dual_55")
    
    if not sample_dataset_path.exists():
        logging.error(f"示例数据集不存在: {sample_dataset_path}")
        return False
    
    logging.info("测试干运行模式...")
    
    try:
        # 加载数据集进行兼容性检查
        from merge_datasets import _validate_dataset_compatibility
        
        dataset1 = _load_local_dataset_safely("test1", sample_dataset_path)
        dataset2 = _load_local_dataset_safely("test2", sample_dataset_path)
        
        _validate_dataset_compatibility([dataset1, dataset2])
        logging.info("✓ 干运行模式测试通过")
        return True
        
    except Exception as e:
        logging.error(f"✗ 干运行模式测试失败: {e}")
        return False


def main():
    """主测试函数"""
    init_logging()
    
    logging.info("=" * 50)
    logging.info("开始数据集合并功能测试")
    logging.info("=" * 50)
    
    # 测试1: 干运行模式
    logging.info("\n测试1: 干运行模式")
    if not test_dry_run():
        logging.error("干运行模式测试失败")
        return
    
    # 测试2: 实际合并
    logging.info("\n测试2: 实际数据集合并")
    if not test_merge_with_sample_dataset():
        logging.error("数据集合并测试失败")
        return
    
    logging.info("\n" + "=" * 50)
    logging.info("所有测试通过！数据集合并功能正常工作")
    logging.info("=" * 50)


if __name__ == "__main__":
    main()
