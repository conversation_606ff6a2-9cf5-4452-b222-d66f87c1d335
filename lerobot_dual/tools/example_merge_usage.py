#!/usr/bin/env python

"""
数据集合并功能使用示例
"""

import logging
from pathlib import Path
from merge_datasets import merge_datasets
from lerobot.common.utils.utils import init_logging


def example_merge_datasets():
    """演示如何使用数据集合并功能"""
    
    # 示例数据集路径
    dataset1_path = Path("so101_dual_55")
    
    # 检查示例数据集是否存在
    if not dataset1_path.exists():
        logging.error(f"示例数据集不存在: {dataset1_path}")
        logging.info("请确保示例数据集路径正确")
        return
    
    logging.info("数据集合并功能使用示例")
    logging.info("=" * 50)
    
    # 方法1: 使用Python API
    logging.info("\n方法1: 使用Python API进行数据集合并")
    
    try:
        # 假设我们有多个数据集要合并（这里用同一个数据集演示）
        dataset_paths = [
            dataset1_path,
            # 在实际使用中，这里应该是不同的数据集路径
            # Path("path/to/dataset2"),
            # Path("path/to/dataset3"),
        ]
        
        output_path = Path("lerobot_dual/tools/merged_output_example")
        
        logging.info(f"输入数据集: {[str(p) for p in dataset_paths]}")
        logging.info(f"输出路径: {output_path}")
        
        # 注意：这里只有一个数据集，实际使用时需要至少两个数据集
        if len(dataset_paths) >= 2:
            merged_dataset = merge_datasets(
                dataset_paths=dataset_paths,
                output_path=output_path,
                output_repo_id="example_merged_dataset",
                backup=True  # 创建备份
            )
            
            logging.info("合并完成！")
            logging.info(f"合并后的数据集信息:")
            logging.info(f"  Episodes: {merged_dataset.meta.total_episodes}")
            logging.info(f"  Frames: {merged_dataset.meta.total_frames}")
            logging.info(f"  Tasks: {merged_dataset.meta.total_tasks}")
        else:
            logging.info("示例中只有一个数据集，跳过实际合并")
            logging.info("在实际使用中，请提供至少两个不同的数据集路径")
        
    except Exception as e:
        logging.error(f"合并失败: {e}")


def example_command_line_usage():
    """演示命令行使用方法"""
    
    logging.info("\n方法2: 命令行使用示例")
    logging.info("=" * 30)
    
    logging.info("基本用法:")
    logging.info("python merge_datasets.py --datasets /path/to/dataset1 /path/to/dataset2 --output /path/to/merged_dataset")
    
    logging.info("\n高级用法:")
    logging.info("python merge_datasets.py \\")
    logging.info("  --datasets /path/to/dataset1 /path/to/dataset2 /path/to/dataset3 \\")
    logging.info("  --output /path/to/merged_dataset \\")
    logging.info("  --repo-id my_merged_dataset \\")
    logging.info("  --backup")
    
    logging.info("\n干运行模式（仅验证兼容性）:")
    logging.info("python merge_datasets.py \\")
    logging.info("  --datasets /path/to/dataset1 /path/to/dataset2 \\")
    logging.info("  --output /path/to/merged_dataset \\")
    logging.info("  --dry-run")
    
    logging.info("\n使用示例数据集的命令:")
    logging.info("# 首先复制示例数据集创建第二个数据集")
    logging.info("cp -r lerobot_dual/tools/so101_dual_55 lerobot_dual/tools/so101_dual_55_copy")
    logging.info("")
    logging.info("# 然后执行合并")
    logging.info("python lerobot_dual/tools/merge_datasets.py \\")
    logging.info("  --datasets lerobot_dual/tools/so101_dual_55 lerobot_dual/tools/so101_dual_55_copy \\")
    logging.info("  --output lerobot_dual/tools/merged_so101_dual \\")
    logging.info("  --repo-id merged_so101_dual \\")
    logging.info("  --backup")


def example_features_and_benefits():
    """介绍功能特性和优势"""
    
    logging.info("\n功能特性:")
    logging.info("=" * 20)
    
    features = [
        "✓ 支持合并多个LeRobot数据集",
        "✓ 自动重新编号episodes和frames",
        "✓ 保持数据完整性和一致性",
        "✓ 支持视频和parquet数据文件",
        "✓ 兼容性验证确保数据集可以安全合并",
        "✓ 备份功能保护现有数据",
        "✓ 干运行模式用于预先验证",
        "✓ 详细的日志记录和错误处理",
        "✓ 支持本地数据集（无需HuggingFace Hub）"
    ]
    
    for feature in features:
        logging.info(f"  {feature}")
    
    logging.info("\n使用场景:")
    logging.info("=" * 20)
    
    use_cases = [
        "• 合并来自不同收集会话的数据",
        "• 组合多个小数据集创建大型训练集",
        "• 整合不同环境或条件下的数据",
        "• 创建多任务数据集",
        "• 数据集版本管理和增量更新"
    ]
    
    for use_case in use_cases:
        logging.info(f"  {use_case}")


def example_best_practices():
    """最佳实践建议"""
    
    logging.info("\n最佳实践:")
    logging.info("=" * 20)
    
    practices = [
        "1. 合并前先使用 --dry-run 验证兼容性",
        "2. 始终使用 --backup 选项保护现有数据",
        "3. 确保所有数据集具有相同的特征结构",
        "4. 检查机器人类型和FPS的一致性",
        "5. 为合并后的数据集选择有意义的repo-id",
        "6. 在合并大型数据集前先测试小样本",
        "7. 定期验证合并后数据的完整性",
        "8. 保留原始数据集的备份"
    ]
    
    for practice in practices:
        logging.info(f"  {practice}")


def main():
    """主函数"""
    init_logging()
    
    logging.info("LeRobot数据集合并工具使用指南")
    logging.info("=" * 60)
    
    # 演示Python API使用
    example_merge_datasets()
    
    # 演示命令行使用
    example_command_line_usage()
    
    # 介绍功能特性
    example_features_and_benefits()
    
    # 最佳实践
    example_best_practices()
    
    logging.info("\n" + "=" * 60)
    logging.info("更多信息请参考代码注释和文档")
    logging.info("=" * 60)


if __name__ == "__main__":
    main()
