#!/usr/bin/env python

# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import argparse
import logging
import shutil
import json
import pandas as pd
from copy import deepcopy
from pathlib import Path
from typing import List, Union

from lerobot.common.datasets.compute_stats import aggregate_stats
from lerobot.common.datasets.lerobot_dataset import LeRobotDataset, LeRobotDatasetMetadata
from lerobot.common.datasets.utils import (
    EPISODES_PATH,
    EPISODES_STATS_PATH,
    INFO_PATH,
    TASKS_PATH,
    write_episode,
    write_episode_stats,
    write_info,
    load_info,
    load_episodes,
    load_tasks,
    load_episodes_stats,
    create_empty_dataset_info,
)
from lerobot.common.utils.utils import init_logging


def _load_local_dataset_safely(repo_id: str, root_path: Path) -> LeRobotDataset:
    """
    安全地加载本地数据集，避免从HuggingFace Hub下载
    """
    from datasets import load_dataset
    from lerobot.common.datasets.utils import (
        load_info, load_episodes, load_tasks, load_episodes_stats, load_stats,
        get_episode_data_index, hf_transform_to_torch
    )
    from lerobot.common.datasets.lerobot_dataset import CODEBASE_VERSION
    from lerobot.common.datasets.video_utils import get_safe_default_codec

    # 创建一个LeRobotDataset对象，但绕过网络检查
    dataset = LeRobotDataset.__new__(LeRobotDataset)

    # 手动设置基本属性
    dataset.repo_id = repo_id
    dataset.root = root_path
    dataset.image_transforms = None
    dataset.delta_timestamps = None
    dataset.episodes = None
    dataset.tolerance_s = 1e-4
    dataset.revision = CODEBASE_VERSION
    dataset.video_backend = get_safe_default_codec()
    dataset.delta_indices = None
    dataset.image_writer = None
    dataset.episode_buffer = None

    # 手动创建metadata对象，避免网络调用
    meta = LeRobotDatasetMetadata.__new__(LeRobotDatasetMetadata)
    meta.repo_id = repo_id
    meta.root = root_path
    meta.revision = CODEBASE_VERSION

    # 直接加载本地metadata文件
    meta.info = load_info(root_path)
    meta.tasks, meta.task_to_task_index = load_tasks(root_path)
    meta.episodes = load_episodes(root_path)

    # 尝试加载episodes_stats，如果不存在则使用空字典
    try:
        meta.episodes_stats = load_episodes_stats(root_path)
    except FileNotFoundError:
        meta.episodes_stats = {}

    # 尝试加载stats，如果不存在则使用空字典
    try:
        from lerobot.common.datasets.utils import load_stats
        meta.stats = load_stats(root_path)
    except FileNotFoundError:
        meta.stats = {}

    dataset.meta = meta

    # 加载HuggingFace数据集
    data_path = str(root_path / "data")
    hf_dataset = load_dataset("parquet", data_dir=data_path, split="train")
    hf_dataset.set_transform(hf_transform_to_torch)
    dataset.hf_dataset = hf_dataset

    # 设置episode数据索引
    dataset.episode_data_index = get_episode_data_index(meta.episodes, None)

    return dataset


def merge_datasets(
    dataset_paths: List[Union[str, Path]],
    output_path: Union[str, Path],
    output_repo_id: str = "merged_dataset",
    backup: bool = False,
) -> LeRobotDataset:
    """
    合并多个LeRobot数据集到一个新的数据集中
    
    Args:
        dataset_paths: 要合并的数据集路径列表
        output_path: 输出数据集的路径
        output_repo_id: 输出数据集的repo_id
        backup: 是否创建备份
        
    Returns:
        合并后的LeRobotDataset对象
    """
    dataset_paths = [Path(p) for p in dataset_paths]
    output_path = Path(output_path)
    
    if len(dataset_paths) < 2:
        raise ValueError("至少需要两个数据集进行合并")
    
    # 验证所有数据集路径存在
    for path in dataset_paths:
        if not path.exists():
            raise FileNotFoundError(f"数据集路径不存在: {path}")
        if not (path / "meta" / "info.json").exists():
            raise FileNotFoundError(f"数据集元数据文件不存在: {path / 'meta' / 'info.json'}")
    
    logging.info(f"开始合并 {len(dataset_paths)} 个数据集")
    
    # 加载所有数据集
    datasets = []
    for i, path in enumerate(dataset_paths):
        logging.info(f"加载数据集 {i+1}/{len(dataset_paths)}: {path}")
        repo_id = path.name  # 使用目录名作为repo_id
        dataset = _load_local_dataset_safely(repo_id, path)
        datasets.append(dataset)
    
    # 验证数据集兼容性
    _validate_dataset_compatibility(datasets)
    
    # 创建输出目录
    if output_path.exists():
        if backup:
            backup_path = output_path.parent / f"{output_path.name}_backup"
            if backup_path.exists():
                shutil.rmtree(backup_path)
            shutil.move(output_path, backup_path)
            logging.info(f"已备份现有数据集到: {backup_path}")
        else:
            shutil.rmtree(output_path)
    
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 合并数据集
    merged_dataset = _merge_datasets_internal(datasets, output_path, output_repo_id)
    
    logging.info(f"数据集合并完成，输出路径: {output_path}")
    logging.info(f"合并后的数据集包含 {merged_dataset.meta.total_episodes} 个episodes，"
                f"{merged_dataset.meta.total_frames} 个frames")
    
    return merged_dataset


def _validate_dataset_compatibility(datasets: List[LeRobotDataset]) -> None:
    """验证数据集是否兼容（具有相同的特征结构）"""
    if not datasets:
        return
    
    reference_features = datasets[0].meta.info["features"]
    reference_robot_type = datasets[0].meta.info["robot_type"]
    reference_fps = datasets[0].meta.info["fps"]
    
    for i, dataset in enumerate(datasets[1:], 1):
        # 检查特征兼容性
        if dataset.meta.info["features"] != reference_features:
            raise ValueError(f"数据集 {i+1} 的特征结构与第一个数据集不兼容")
        
        # 检查机器人类型
        if dataset.meta.info["robot_type"] != reference_robot_type:
            logging.warning(f"数据集 {i+1} 的机器人类型 ({dataset.meta.info['robot_type']}) "
                          f"与第一个数据集 ({reference_robot_type}) 不同")
        
        # 检查FPS
        if dataset.meta.info["fps"] != reference_fps:
            logging.warning(f"数据集 {i+1} 的FPS ({dataset.meta.info['fps']}) "
                          f"与第一个数据集 ({reference_fps}) 不同")
    
    logging.info("数据集兼容性验证通过")


def _merge_datasets_internal(
    datasets: List[LeRobotDataset], 
    output_path: Path, 
    output_repo_id: str
) -> LeRobotDataset:
    """内部合并数据集的实现"""
    
    # 使用第一个数据集作为模板创建新的元数据
    reference_dataset = datasets[0]
    merged_info = deepcopy(reference_dataset.meta.info)
    
    # 重置计数器
    merged_info["total_episodes"] = 0
    merged_info["total_frames"] = 0
    merged_info["total_tasks"] = 0
    merged_info["total_videos"] = 0
    merged_info["total_chunks"] = 0
    
    # 创建输出目录结构
    (output_path / "meta").mkdir(parents=True, exist_ok=True)
    (output_path / "data" / "chunk-000").mkdir(parents=True, exist_ok=True)
    if merged_info.get("video_path"):
        for video_key in reference_dataset.meta.video_keys:
            (output_path / "videos" / "chunk-000" / video_key).mkdir(parents=True, exist_ok=True)
    
    # 合并episodes、tasks和stats
    merged_episodes = {}
    merged_episodes_stats = {}
    all_tasks = set()
    current_episode_index = 0
    current_frame_offset = 0
    
    for dataset_idx, dataset in enumerate(datasets):
        logging.info(f"处理数据集 {dataset_idx + 1}/{len(datasets)}: {dataset.repo_id}")
        
        # 收集所有任务
        for ep in dataset.meta.episodes.values():
            if "tasks" in ep:
                all_tasks.update(ep["tasks"])
        
        # 复制episodes和相关文件
        for old_ep_idx in sorted(dataset.meta.episodes.keys()):
            episode = dataset.meta.episodes[old_ep_idx]
            episode_stats = dataset.meta.episodes_stats.get(old_ep_idx, {})
            
            # 更新episode索引
            new_episode = deepcopy(episode)
            new_episode["episode_index"] = current_episode_index
            merged_episodes[current_episode_index] = new_episode
            
            if episode_stats:
                merged_episodes_stats[current_episode_index] = episode_stats
            
            # 复制parquet文件
            _copy_episode_data_file(dataset, old_ep_idx, output_path, current_episode_index, current_frame_offset)
            
            # 复制视频文件
            _copy_episode_video_files(dataset, old_ep_idx, output_path, current_episode_index)
            
            current_frame_offset += episode["length"]
            current_episode_index += 1
    
    # 更新合并后的信息
    merged_info["total_episodes"] = len(merged_episodes)
    merged_info["total_frames"] = sum(ep["length"] for ep in merged_episodes.values())
    merged_info["total_tasks"] = len(all_tasks)
    merged_info["total_videos"] = merged_info["total_episodes"] * len(reference_dataset.meta.video_keys) if reference_dataset.meta.video_keys else 0
    merged_info["total_chunks"] = 1  # 假设所有数据都在一个chunk中
    merged_info["splits"] = {"train": f"0:{merged_info['total_episodes']}"}
    
    # 写入元数据文件
    write_info(merged_info, output_path)
    
    # 写入episodes
    for ep in sorted(merged_episodes.values(), key=lambda x: x["episode_index"]):
        write_episode(ep, output_path)
    
    # 写入tasks
    tasks_path = output_path / TASKS_PATH
    with open(tasks_path, "w") as f:
        for task_idx, task in enumerate(sorted(all_tasks)):
            f.write(json.dumps({"task_index": task_idx, "task": task}) + "\n")
    
    # 写入episodes_stats
    for ep_idx, stats in merged_episodes_stats.items():
        write_episode_stats(ep_idx, stats, output_path)
    
    # 创建并返回合并后的数据集
    merged_dataset = _load_local_dataset_safely(output_repo_id, output_path)

    return merged_dataset


def _copy_episode_data_file(
    source_dataset: LeRobotDataset,
    source_episode_idx: int,
    output_path: Path,
    target_episode_idx: int,
    frame_offset: int
) -> None:
    """复制episode的parquet数据文件并更新索引"""
    source_file = source_dataset.root / source_dataset.meta.get_data_file_path(source_episode_idx)
    target_file = output_path / f"data/chunk-000/episode_{target_episode_idx:06d}.parquet"

    if not source_file.exists():
        logging.warning(f"源数据文件不存在: {source_file}")
        return

    # 读取parquet文件
    df = pd.read_parquet(source_file)

    # 更新索引
    df['episode_index'] = target_episode_idx
    df['index'] = df['frame_index'] + frame_offset

    # 保存到目标位置
    target_file.parent.mkdir(parents=True, exist_ok=True)
    df.to_parquet(target_file, index=False)

    logging.debug(f"已复制数据文件: {source_file} -> {target_file}")


def _copy_episode_video_files(
    source_dataset: LeRobotDataset,
    source_episode_idx: int,
    output_path: Path,
    target_episode_idx: int
) -> None:
    """复制episode的视频文件"""
    if not source_dataset.meta.video_keys:
        return

    for video_key in source_dataset.meta.video_keys:
        source_file = source_dataset.root / source_dataset.meta.get_video_file_path(source_episode_idx, video_key)
        target_file = output_path / f"videos/chunk-000/{video_key}/episode_{target_episode_idx:06d}.mp4"

        if source_file.exists():
            target_file.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(source_file, target_file)
            logging.debug(f"已复制视频文件: {source_file} -> {target_file}")
        else:
            logging.warning(f"源视频文件不存在: {source_file}")


def main():
    parser = argparse.ArgumentParser(description="合并多个LeRobot数据集")

    parser.add_argument(
        "--datasets",
        type=str,
        nargs="+",
        required=True,
        help="要合并的数据集路径列表，用空格分隔"
    )

    parser.add_argument(
        "--output",
        type=Path,
        required=True,
        help="输出数据集的路径"
    )

    parser.add_argument(
        "--repo-id",
        type=str,
        default="merged_dataset",
        help="输出数据集的repo_id（默认: merged_dataset）"
    )

    parser.add_argument(
        "--backup",
        action="store_true",
        help="如果输出路径已存在，是否创建备份"
    )

    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="仅验证数据集兼容性，不执行实际合并"
    )

    args = parser.parse_args()

    # 验证输入参数
    if len(args.datasets) < 2:
        logging.error("至少需要指定两个数据集进行合并")
        return

    dataset_paths = [Path(p) for p in args.datasets]

    # 验证数据集路径
    for path in dataset_paths:
        if not path.exists():
            logging.error(f"数据集路径不存在: {path}")
            return
        if not (path / "meta" / "info.json").exists():
            logging.error(f"数据集元数据文件不存在: {path / 'meta' / 'info.json'}")
            return

    logging.info(f"准备合并以下数据集:")
    for i, path in enumerate(dataset_paths, 1):
        logging.info(f"  {i}. {path}")

    if args.dry_run:
        logging.info("执行干运行模式，仅验证兼容性...")
        # 加载所有数据集进行兼容性检查
        datasets = []
        for path in dataset_paths:
            repo_id = path.name
            dataset = _load_local_dataset_safely(repo_id, path)
            datasets.append(dataset)
            logging.info(f"  数据集 {path.name}: {dataset.meta.total_episodes} episodes, "
                        f"{dataset.meta.total_frames} frames")

        try:
            _validate_dataset_compatibility(datasets)
            logging.info("✓ 所有数据集兼容，可以进行合并")
        except Exception as e:
            logging.error(f"✗ 数据集兼容性检查失败: {e}")
        return

    try:
        # 执行合并
        merged_dataset = merge_datasets(
            dataset_paths=dataset_paths,
            output_path=args.output,
            output_repo_id=args.repo_id,
            backup=args.backup
        )

        logging.info("数据集合并成功完成！")
        logging.info(f"输出路径: {args.output}")
        logging.info(f"总episodes: {merged_dataset.meta.total_episodes}")
        logging.info(f"总frames: {merged_dataset.meta.total_frames}")
        logging.info(f"总tasks: {merged_dataset.meta.total_tasks}")

    except Exception as e:
        logging.error(f"数据集合并失败: {e}")
        raise


if __name__ == "__main__":
    init_logging()
    main()
